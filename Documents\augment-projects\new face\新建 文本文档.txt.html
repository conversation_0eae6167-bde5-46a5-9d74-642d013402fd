<!doctype html>
<html lang="zh-CN">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<title>无领导小组面试手册 — 封面</title>
<link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;600;700;900&display=swap" rel="stylesheet">
<style>
  /* 固定封面尺寸：1600 x 2560 px */
  :root{
    --w:1600px;
    --h:2560px;
    --primary:#0f4c81;
    --secondary:#1aa3d8;
    --accent:#ffd166;
    --bg:#f5f8fb;
    --text:#0b2132;
  }
  html,body{height:100%;margin:0;padding:0;background:var(--bg);font-family:"Noto Sans SC", system-ui, -apple-system, "Helvetica Neue", Arial;}
  /* 居中画布 */
  .canvas-wrap{
    width:100%;
    min-height:100%;
    display:flex;
    align-items:center;
    justify-content:center;
    padding:40px;
    box-sizing:border-box;
  }

  .cover{
    width:var(--w);
    height:var(--h);
    background: linear-gradient(180deg, var(--primary) 0%, var(--secondary) 100%);
    border-radius:14px;
    overflow:hidden;
    box-shadow: 0 30px 60px rgba(12,30,50,0.12);
    color:white;
    display:flex;
    flex-direction:column;
  }

  /* 顶部状态栏（平面化装饰） */
  .top{
    padding:48px 72px;
    display:flex;
    justify-content:space-between;
    align-items:center;
  }
  .tag{
    background: rgba(255,255,255,0.12);
    padding:10px 14px;
    border-radius:999px;
    font-weight:600;
    letter-spacing:0.4px;
    font-size:16px;
  }
  .edition{font-size:15px;opacity:0.95;font-weight:600;}

  /* 主体标题 */
  .main{
    flex:1;
    display:flex;
    padding: 80px 72px 80px 72px;
    align-items:center;
    justify-content:space-between;
    box-sizing:border-box;
    gap: 60px;
  }
  .title-block{
    width:50%;
    max-width:650px;
  }
  .title{
    font-size:88px;
    line-height:1.05;
    font-weight:900;
    margin:0 0 20px 0;
    text-shadow: none;
    letter-spacing:0.5px;
  }
  .subtitle{
    font-size:24px;
    font-weight:600;
    margin-bottom:32px;
    opacity:0.96;
    line-height:1.4;
  }
  .divider{
    width:140px;height:8px;border-radius:8px;background:var(--accent);margin-bottom:28px;
  }

  /* 插画区域 */
  .art{
    width:50%;
    display:flex;
    align-items:center;
    justify-content:center;
    padding-left:40px;
  }

  /* 底部作者栏（无出版社） */
  .bottom{
    padding:36px 72px;
    display:flex;
    justify-content:space-between;
    align-items:center;
    border-top: 1px solid rgba(255,255,255,0.06);
    background: linear-gradient(180deg, rgba(255,255,255,0.02), rgba(255,255,255,0.00));
  }
  .left-meta{font-size:15px;opacity:0.95;}
  .author{font-weight:700;font-size:20px;letter-spacing:0.6px;}

  /* 响应式：缩小页面时自适应（仅用于预览） */
  @media (max-width:1200px){
    :root{--w:900px; --h:1440px;}
    .title{font-size:56px;}
    .subtitle{font-size:16px;}
    .cover{border-radius:10px;}
  }

  /* 让页面背景在打印时不显示多余边距 */
  @media print {
    body {margin:0;}
    .canvas-wrap{padding:0;}
    .cover{box-shadow:none;border-radius:0;}
  }
</style>
</head>
<body>
  <div class="canvas-wrap">
    <div class="cover" role="img" aria-label="无领导小组面试手册 封面">
      <div class="top">
        <div class="tag">面试技巧 · 实战指南</div>
        <div class="edition">第1版</div>
      </div>

      <div class="main">
        <div class="title-block">
          <h1 class="title">无领导小组面试手册</h1>
          <div class="subtitle">结构化思路 · 发言策略 · 小组协作赢面试</div>
          <div class="divider" aria-hidden="true"></div>
          <p style="margin:24px 0 0 0;font-size:16px;line-height:1.65;opacity:0.94;max-width:580px;">
            本书涵盖评分要点、常见题型、发言顺序建议与实战模拟，帮助你在无领导小组讨论中更有逻辑、更能突出贡献与领导力。
          </p>
        </div>

        <div class="art" aria-hidden="true">
          <!-- 扁平风矢量插画 -->
          <svg width="420" height="420" viewBox="0 0 420 420" xmlns="http://www.w3.org/2000/svg" role="img" aria-label="讨论插画">
            <defs>
              <linearGradient id="a" x1="0" x2="1">
                <stop offset="0" stop-color="#ffd166"/>
                <stop offset="1" stop-color="#ff8c42"/>
              </linearGradient>
              <linearGradient id="b" x1="0" x2="1">
                <stop offset="0" stop-color="rgba(255,255,255,0.15)"/>
                <stop offset="1" stop-color="rgba(255,255,255,0.05)"/>
              </linearGradient>
            </defs>
            <rect x="0" y="0" width="420" height="420" rx="32" fill="rgba(255,255,255,0.08)"/>
            <!-- 中心人物 -->
            <g transform="translate(140,80)">
              <circle cx="70" cy="50" r="45" fill="#fff" opacity="0.98"/>
              <rect x="25" y="130" width="90" height="55" rx="12" fill="url(#a)"/>
            </g>
            <!-- 其他成员 -->
            <g transform="translate(20,180)" opacity="0.92">
              <circle cx="45" cy="25" r="25" fill="#fff" />
              <rect x="8" y="55" width="74" height="32" rx="8" fill="url(#b)"/>
            </g>
            <g transform="translate(280,180)" opacity="0.92">
              <circle cx="45" cy="25" r="25" fill="#fff" />
              <rect x="8" y="55" width="74" height="32" rx="8" fill="url(#b)"/>
            </g>
            <!-- 底部阴影 -->
            <ellipse cx="210" cy="380" rx="160" ry="25" fill="rgba(0,0,0,0.08)"/>
            <!-- 装饰元素 -->
            <circle cx="80" cy="80" r="8" fill="var(--accent)" opacity="0.6"/>
            <circle cx="340" cy="120" r="6" fill="var(--accent)" opacity="0.4"/>
            <circle cx="60" cy="320" r="4" fill="#fff" opacity="0.3"/>
          </svg>
        </div>
      </div>

      <div class="bottom">
        <div class="left-meta">包含评分要点与表现建议 · 含模拟题</div>
        <div class="author">作者：小红书@MinNan</div>
      </div>
    </div>
  </div>
</body>
</html>