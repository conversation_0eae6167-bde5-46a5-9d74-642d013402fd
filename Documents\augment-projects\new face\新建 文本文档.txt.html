<!doctype html>
<html lang="zh-CN">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<title>无领导小组面试手册 — 封面</title>
<link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;600;700;900&display=swap" rel="stylesheet">
<style>
  /* 固定封面尺寸：1600 x 2560 px */
  :root{
    --w:1600px;
    --h:2560px;
    --primary:#0f4c81;
    --secondary:#1aa3d8;
    --accent:#ffd166;
    --bg:#f5f8fb;
    --text:#0b2132;
  }
  html,body{height:100%;margin:0;padding:0;background:var(--bg);font-family:"Noto Sans SC", system-ui, -apple-system, "Helvetica Neue", Arial;}
  /* 居中画布 */
  .canvas-wrap{
    width:100%;
    min-height:100%;
    display:flex;
    align-items:center;
    justify-content:center;
    padding:40px;
    box-sizing:border-box;
  }

  .cover{
    width:var(--w);
    height:var(--h);
    background: linear-gradient(180deg, var(--primary) 0%, var(--secondary) 100%);
    border-radius:14px;
    overflow:hidden;
    box-shadow: 0 30px 60px rgba(12,30,50,0.12);
    color:white;
    display:flex;
    flex-direction:column;
  }

  /* 顶部状态栏（平面化装饰） */
  .top{
    padding:48px 72px;
    display:flex;
    justify-content:space-between;
    align-items:center;
  }
  .tag{
    background: rgba(255,255,255,0.12);
    padding:16px 24px;
    border-radius:999px;
    font-weight:600;
    letter-spacing:0.4px;
    font-size:28px;
  }
  .edition{font-size:26px;opacity:0.95;font-weight:600;}

  /* 主体标题 */
  .main{
    flex:1;
    display:flex;
    flex-direction:column;
    padding: 60px 72px 40px 72px;
    justify-content:center;
    box-sizing:border-box;
    gap: 50px;
  }
  .title-block{
    width:100%;
    max-width:900px;
    text-align:left;
  }
  .title{
    font-size:88px;
    line-height:1.05;
    font-weight:900;
    margin:0 0 20px 0;
    text-shadow: none;
    letter-spacing:0.5px;
  }
  .subtitle{
    font-size:42px;
    font-weight:600;
    margin-bottom:40px;
    opacity:0.96;
    line-height:1.4;
  }
  .divider{
    width:200px;height:12px;border-radius:12px;background:var(--accent);margin-bottom:36px;
  }

  /* 插画区域 */
  .art{
    width:100%;
    display:flex;
    align-items:center;
    justify-content:center;
    padding:0;
  }

  /* 底部作者栏（无出版社） */
  .bottom{
    padding:36px 72px;
    display:flex;
    justify-content:space-between;
    align-items:center;
    border-top: 1px solid rgba(255,255,255,0.06);
    background: linear-gradient(180deg, rgba(255,255,255,0.02), rgba(255,255,255,0.00));
  }
  .left-meta{font-size:26px;opacity:0.95;}
  .author{font-weight:700;font-size:32px;letter-spacing:0.6px;}

  /* 响应式：缩小页面时自适应（仅用于预览） */
  @media (max-width:1200px){
    :root{--w:900px; --h:1440px;}
    .title{font-size:52px;}
    .subtitle{font-size:20px;}
    .cover{border-radius:10px;}
    .main{padding: 50px 40px 30px 40px; gap: 35px;}
    .tag{font-size:16px; padding:10px 15px;}
    .edition{font-size:15px;}
    .left-meta{font-size:15px;}
    .author{font-size:20px;}
  }

  /* 让页面背景在打印时不显示多余边距 */
  @media print {
    body {margin:0;}
    .canvas-wrap{padding:0;}
    .cover{box-shadow:none;border-radius:0;}
  }
</style>
</head>
<body>
  <div class="canvas-wrap">
    <div class="cover" role="img" aria-label="无领导小组面试手册 封面">
      <div class="top">
        <div class="tag">面试技巧 · 实战指南</div>
        <div class="edition">第1版</div>
      </div>

      <div class="main">
        <div class="title-block">
          <h1 class="title">无领导小组面试手册</h1>
          <div class="subtitle">结构化思路 · 发言策略 · 小组协作赢面试</div>
          <div class="divider" aria-hidden="true"></div>
          <p style="margin:32px 0 0 0;font-size:30px;line-height:1.6;opacity:0.94;max-width:720px;">
            本书涵盖评分要点、常见题型、发言顺序建议与实战模拟，帮助你在无领导小组讨论中更有逻辑、更能突出贡献与领导力。
          </p>
        </div>

        <div class="art" aria-hidden="true">
          <!-- 扁平风矢量插画 -->
          <svg width="500" height="375" viewBox="0 0 480 360" xmlns="http://www.w3.org/2000/svg" role="img" aria-label="讨论插画">
            <defs>
              <linearGradient id="a" x1="0" x2="1">
                <stop offset="0" stop-color="#ffd166"/>
                <stop offset="1" stop-color="#ff8c42"/>
              </linearGradient>
              <linearGradient id="b" x1="0" x2="1">
                <stop offset="0" stop-color="rgba(255,255,255,0.15)"/>
                <stop offset="1" stop-color="rgba(255,255,255,0.05)"/>
              </linearGradient>
            </defs>
            <rect x="0" y="0" width="480" height="360" rx="24" fill="rgba(255,255,255,0.08)"/>
            <!-- 中心人物 -->
            <g transform="translate(190,60)">
              <circle cx="50" cy="40" r="35" fill="#fff" opacity="0.98"/>
              <rect x="15" y="100" width="70" height="45" rx="10" fill="url(#a)"/>
            </g>
            <!-- 左侧成员 -->
            <g transform="translate(60,120)" opacity="0.92">
              <circle cx="35" cy="20" r="20" fill="#fff" />
              <rect x="8" y="45" width="54" height="25" rx="6" fill="url(#b)"/>
            </g>
            <!-- 右侧成员 -->
            <g transform="translate(320,120)" opacity="0.92">
              <circle cx="35" cy="20" r="20" fill="#fff" />
              <rect x="8" y="45" width="54" height="25" rx="6" fill="url(#b)"/>
            </g>
            <!-- 底部成员 -->
            <g transform="translate(120,220)" opacity="0.88">
              <circle cx="30" cy="15" r="15" fill="#fff" />
              <rect x="8" y="35" width="44" height="20" rx="5" fill="url(#b)"/>
            </g>
            <g transform="translate(280,220)" opacity="0.88">
              <circle cx="30" cy="15" r="15" fill="#fff" />
              <rect x="8" y="35" width="44" height="20" rx="5" fill="url(#b)"/>
            </g>
            <!-- 底部阴影 -->
            <ellipse cx="240" cy="320" rx="120" ry="18" fill="rgba(0,0,0,0.06)"/>
            <!-- 装饰元素 -->
            <circle cx="80" cy="60" r="6" fill="var(--accent)" opacity="0.6"/>
            <circle cx="400" cy="80" r="4" fill="var(--accent)" opacity="0.4"/>
            <circle cx="40" cy="280" r="3" fill="#fff" opacity="0.3"/>
            <circle cx="440" cy="300" r="3" fill="#fff" opacity="0.3"/>
          </svg>
        </div>
      </div>

      <div class="bottom">
        <div class="left-meta">包含评分要点与表现建议 · 含模拟题</div>
        <div class="author">作者：小红书@MinNan</div>
      </div>
    </div>
  </div>
</body>
</html>